import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'

describe('HTTP submit assessment controller', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns success when assessment is submitted successfully', async () => {
    const mockSubmissionResult = {
      Id: '550e8400-e29b-41d4-a716-446655440000',
      Passed: true,
      Score: 85,
      maxScore: 100,
      responsesCount: 10,
      gradedQuestionsCount: 10,
      pendingQuestionsCount: 0
    }

    const submitAssessmentStub = Sinon.stub().resolves(mockSubmissionResult)
    const getSessionStub = Sinon.stub().resolves({
      fields: { Start: new Date('2023-01-01T10:00:00Z') }
    })
    const updateSessionStub = Sinon.stub().resolves({})

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/internal/submit-assessment-service.js': {
        default: submitAssessmentStub
      },
      '../../../services/mssql/session/get.service.js': {
        default: getSessionStub
      },
      '../../../services/mssql/session/update.service.js': {
        default: updateSessionStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      params: { id: '550e8400-e29b-41d4-a716-446655440000' },
      body: [
        {
          QuestionId: '550e8400-e29b-41d4-a716-446655440010',
          QuestionVersion: 1,
          OptionId: '550e8400-e29b-41d4-a716-446655440011',
          OptionVersion: 1,
          Duration: 'PT30S'
        }
      ]
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.NO_CONTENT)
  })

  it('handles optional notes in request', async () => {
    const mockSubmissionResult = {
      Id: '550e8400-e29b-41d4-a716-446655440001',
      Passed: false,
      Score: 60,
      maxScore: 100,
      responsesCount: 5,
      gradedQuestionsCount: 5,
      pendingQuestionsCount: 0
    }

    const submitAssessmentStub = Sinon.stub().resolves(mockSubmissionResult)
    const getSessionStub = Sinon.stub().resolves({
      fields: { Start: new Date('2023-01-01T10:00:00Z') }
    })
    const updateSessionStub = Sinon.stub().resolves({})

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/internal/submit-assessment-service.js': {
        default: submitAssessmentStub
      },
      '../../../services/mssql/session/get.service.js': {
        default: getSessionStub
      },
      '../../../services/mssql/session/update.service.js': {
        default: updateSessionStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      params: { id: '550e8400-e29b-41d4-a716-446655440001' },
      body: []
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.NO_CONTENT)
  })

  it('returns 400 for validation errors', async () => {
    const getSessionStub = Sinon.stub().rejects(new Error('Invalid session ID'))

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/mssql/session/get.service.js': {
        default: getSessionStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      params: { id: '550e8400-e29b-41d4-a716-446655440002' },
      body: []
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })

  it('returns 500 for not found errors', async () => {
    const getSessionStub = Sinon.stub().rejects(new Error('Session not found'))

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/mssql/session/get.service.js': {
        default: getSessionStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      params: { id: '550e8400-e29b-41d4-a716-446655440003' },
      body: []
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })

  it('returns 500 for general server errors', async () => {
    const getSessionStub = Sinon.stub().rejects(new Error('Database connection failed'))

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/mssql/session/get.service.js': {
        default: getSessionStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      params: { id: '550e8400-e29b-41d4-a716-446655440004' },
      body: []
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
